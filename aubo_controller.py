#! /usr/bin/env python
# coding=utf-8
"""
AuboController

一个对 Aubo Python SDK(pyaubo_sdk) 的轻量封装，提供更易用的直线/圆弧运动、
速度管理与位置查询接口，便于二次开发。

功能:
- 连接/登录 RPC
- 速度配置缓存（工具端/关节速度和加速度）与设置接口
- 直线运动 moveLine（支持从当前位姿到目标，或先到起点再到终点）
- 获取当前关节角与 TCP 位姿
- 可选阻塞等待运动完成（基于 execId 轮询）

注意:
- SDK 并未提供“全局速度设置”接口。速度/加速度通常作为运动指令参数传入。
  本类在内部缓存“默认速度参数”，并在每次下发运动时使用或被方法参数覆盖。
- 速度单位请遵循示例。示例中将角度单位统一转换为弧度，如 250*(pi/180)。
- 使用前请确保机械臂已上电、解锁，且处于可运动状态（可参考 example_startup.py）。
"""
from __future__ import annotations

import time
import math
from typing import List, Optional, Tuple

import pyaubo_sdk


M_PI = 3.14159265358979323846


class AuboController:
    def __init__(self, ip: str = "127.0.0.1", port: int = 30004,
                 username: str = "aubo", password: str = "123") -> None:
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password

        self._client = pyaubo_sdk.RpcClient()
        self.robot_name: Optional[str] = None

        # 速度参数缓存（默认从 RobotConfig 读取，可被 set_* 覆盖）
        self.tool_speed: Optional[float] = None   # TCP 速度（示例以弧度制传参）
        self.tool_acc: Optional[float] = None     # TCP 加速度
        self.joint_speed: Optional[float] = None  # 关节速度（rad/s）
        self.joint_acc: Optional[float] = None    # 关节加速度（rad/s^2）

    # ---------- 连接/登录 ----------
    def connect(self, login: bool = True) -> bool:
        """连接 RPC（可选自动登录）。成功返回 True。"""
        self._client.connect(self.ip, self.port)
        if not self._client.hasConnected():
            return False
        if login:
            return self.login(self.username, self.password)
        return True

    def login(self, username: str, password: str) -> bool:
        self._client.login(username, password)
        if not self._client.hasLogined():
            return False
        # 选择第一台机器人
        names = self._client.getRobotNames()
        if not names:
            return False
        self.robot_name = names[0]
        # 初始化速度缓存
        self._init_default_speeds()
        return True

    def _assert_ready(self) -> None:
        if not self._client.hasConnected():
            raise RuntimeError("RPC 未连接")
        if not self._client.hasLogined():
            raise RuntimeError("RPC 未登录")
        if not self.robot_name:
            raise RuntimeError("未获取到机器人名称")

    # ---------- 速度相关 ----------
    def _init_default_speeds(self) -> None:
        """从 RobotConfig 读取默认速度参数，作为本类的初始速度。"""
        try:
            iface = self._client.getRobotInterface(self.robot_name)
            cfg = iface.getRobotConfig()
            # 注意: 示例中线速度/加速度与关节速度/加速度单位均以 rad/s、rad/s^2 传参示范
            self.tool_speed = cfg.getDefaultToolSpeed()
            self.tool_acc = cfg.getDefaultToolAcc()
            self.joint_speed = cfg.getDefaultJointSpeed()
            self.joint_acc = cfg.getDefaultJointAcc()
            print(f"tool_speed: {self.tool_speed}")
            print(f"tool_acc: {self.tool_acc}")
        except Exception:
            # 出错时保持为 None，调用运动接口时需提供或回退到示例默认值
            pass

    def set_tool_speed_acc(self, speed: float, acc: float) -> None:
        self.tool_speed = float(speed)
        self.tool_acc = float(acc)

    def set_joint_speed_acc(self, speed: float, acc: float) -> None:
        self.joint_speed = float(speed)
        self.joint_acc = float(acc)

    # ---------- 状态获取 ----------
    def get_joint_positions(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getJointPositions()

    def get_tcp_pose(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getTcpPose()

    # ---------- 运行期控制 ----------
    def start_runtime(self) -> None:
        """开始规划期（示例在发运动前调用）。"""
        self._client.getRuntimeMachine().start()

    def stop_runtime(self) -> None:
        self._client.getRuntimeMachine().stop()

    # ---------- 同步等待 ----------
    def wait_for_motion(self) -> int:
        """阻塞等待当前运动完成，基于 execId 轮询。失败返回 -1，成功无返回值。"""
        self._assert_ready()
        iface = self._client.getRobotInterface(self.robot_name)
        mc = iface.getMotionControl()

        # 等待产生 execId
        cnt = 0
        while mc.getExecId() == -1:
            cnt += 1
            if cnt > 5:
                return -1
            time.sleep(0.05)
            print("getExecId: ", mc.getExecId())
        # 等待 execId 变化（运动结束）
        cur_id = mc.getExecId()
        while True:
            if mc.getExecId() != cur_id:
                break
            time.sleep(0.05)

    # ---------- 运动指令 ----------
    def moveJoint(self, q: List[float],
                   speed: Optional[float] = None,
                   acc: Optional[float] = None,
                   blend_radius: float = 0.0,
                   ref: int = 0,
                   wait: bool = True) -> int:
        """关节运动封装。
        参数含义遵循示例：speed/acc 以 rad/s、rad/s^2 传入；blend 与参考系使用默认值。
        返回 SDK 返回码，0 为成功。
        """
        self._assert_ready()
        spd = float(speed if speed is not None else (self.joint_speed if self.joint_speed is not None else 180 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.joint_acc if self.joint_acc is not None else 1000000 * (M_PI / 180)))

        self.start_runtime()
        ret = self._client.getRobotInterface(self.robot_name).getMotionControl() \
            .moveJoint(q, spd, ac, blend_radius, ref)
        if wait:
            wait_result = self.wait_for_motion()
            # 结束规划期（保持与示例一致）
            self.stop_runtime()
            if wait_result == -1:
                # 等待失败
                return -1 if ret == 0 else ret
        else:
            # 非阻塞也结束规划期交由调用方自行管理，这里不自动 stop
            self.stop_runtime()
        return ret

    def moveLine(self,
                  target_pose: List[float],
                  start_pose: Optional[List[float]] = None,
                  speed: Optional[float] = None,
                  acc: Optional[float] = None,
                  blend_radius: float = 0.0,
                  ref: int = 0,
                  wait: bool = True) -> int:
        """直线运动：
        - 若提供 start_pose，则先直线到 start_pose，再直线到 target_pose；
        - 否则从当前位置直线到 target_pose。
        返回 SDK 返回码，0 为成功。
        """
        self._assert_ready()
        spd = float(speed if speed is not None else (self.tool_speed if self.tool_speed is not None else 250 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.tool_acc if self.tool_acc is not None else 1000 * (M_PI / 180)))

        self.start_runtime()
        mc = self._client.getRobotInterface(self.robot_name).getMotionControl()
        ret = 0
        try:
            if start_pose is not None:
                r1 = mc.moveLine(start_pose, spd, ac, blend_radius, ref)
                if r1 != 0:
                    ret = r1
                    return ret
                if wait and self.wait_for_motion() == -1:
                    return -1
            ret = mc.moveLine(target_pose, spd, ac, blend_radius, ref)
            if wait:
                wait_result = self.wait_for_motion()
                if wait_result == -1:
                    return -1 if ret == 0 else ret
            return ret
        finally:
            self.stop_runtime()


# ---------------------- 使用示例 ----------------------
if __name__ == '__main__':
    ctrl = AuboController(ip="*************", port=30004)
    if not ctrl.connect(login=True):
        print("连接或登录失败")
        raise SystemExit(1)
    print("连接并登录成功，机器人:", ctrl.robot_name)

    # 可选：调整速度（示例中的传值风格）
    ctrl.set_tool_speed_acc(0.05, 0.05)
    ctrl.set_joint_speed_acc(0.1, 0.1)

    # 读取当前状态
    try:
        print("当前关节角:", ctrl.get_joint_positions())
        print("当前TCP位姿:", ctrl.get_tcp_pose())
    except Exception as e:
        print("读取状态失败:", e)

    p1 = ctrl.get_tcp_pose()
    p1[2] += 0.1
    print(f"p1: {p1}")
    # # 直线运动（从当前位置到目标）
    ret = ctrl.moveLine(target_pose=p1, wait=True)
    print("moveLine ret:", ret)

