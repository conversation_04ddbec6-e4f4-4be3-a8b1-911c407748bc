import time

from pyrobotiqgripper import RobotiqGripper

# pyRobotiqGripper
# gripper = RobotiqGripper()
gripper = RobotiqGripper(portname="/dev/ttyUSB0")

gripper.printInfo()

print(gripper.getPosition())
# gripper.reset()
# gripper.activate()
# gripper.calibrate(0, 40)

# gripper.open()
# gripper.close()
# time.sleep(1)
# gripper.open()
# gripper.goTo(100)
# time.sleep(1)
# gripper.goTo(255)
# gripper.goTo(0)
# position_in_bit = gripper.getPosition()
# print(position_in_bit)
# gripper.goTomm(25)
# position_in_mm = gripper.getPositionmm()
# print(position_in_mm)
